{"name": "hello_world", "version": "1.0.0", "description": "Starter app", "main": "index.js", "scripts": {"start": "node index.js", "dev": "node --watch index.js", "test": "echo \"Error: no test specified\" && exit 1", "build:ui": "rm -rf dist && cd ../../part2/notes project/ && npm run build && cp -r dist ../../part3/backend", "deploy:full": "npm run build:ui && git add . && git commit -m buildui && git push"}, "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"dotenv": "^16.5.0", "express": "^5.1.0", "mongodb": "^6.17.0", "mongoose": "^8.15.1", "morgan": "^1.10.0"}}