{"name": "phonebook", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "json-server -p 3001 db.json"}, "dependencies": {"axios": "^1.8.3", "npm": "^11.1.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "json-server": "^1.0.0-beta.3", "vite": "^6.1.0"}}