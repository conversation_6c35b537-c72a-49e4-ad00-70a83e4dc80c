require("dotenv").config();
const express = require("express");
const morgan = require("morgan");
const app = express();
const Entry = require("./models/entry");

app.use(express.static("dist"));
app.use(express.json());
morgan.token("body", (req) => {
  return JSON.stringify(req.body);
});

app.use(
  morgan(":method :url :status :res[content-length] - :response-time ms :body")
);

let phonebook = [
  {
    id: "1",
    name: "<PERSON><PERSON>",
    number: "040-123456",
  },
  {
    id: "2",
    name: "<PERSON>",
    number: "39-44-5323523",
  },
  {
    id: "3",
    name: "<PERSON>",
    number: "12-43-234345",
  },
  {
    id: "4",
    name: "<PERSON>",
    number: "39-23-6423122",
  },
];

app.get("/api/health", (request, response) => {
  response.send("Server is running!");
});

app.get("/api/people", (request, response) => {
  Entry.find({}).then((nums) => {
    response.json(nums);
  });
});

app.get("/api/people/:id", (request, response) => {
  Entry.findById(request.params.id).then((num) => {
    response.json(num);
  });
});

app.delete("/api/people/:id", (request, response) => {
  const id = request.params.id;
  Entry.findByIdAndDelete(id).then((result) => {
    response.status(204).end();
  });
});

app.post("/api/people", (req, res) => {
  const body = req.body;
  if (!body.name || !body.number) {
    return res.status(400).json({
      error: "missing name or number",
    });
  }

  const entry = new Entry({
    name: body.name,
    number: body.number,
  });

  entry.save().then((savedEntry) => {
    res.json(savedEntry);
  });
});

const PORT = process.env.PORT || 3002;
app.listen(PORT, () => {
  console.log(`Server running now on ${PORT}`);
});

const errorHandler = (error, req, res, next) => {
  console.log(error);
  if (error.name === "CastError") {
    return res.status(400).send({ error: "messed up ID" });
  }
  next(error);
};

app.use(errorHandler);
